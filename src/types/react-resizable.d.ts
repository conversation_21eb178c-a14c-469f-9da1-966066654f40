declare module 'react-resizable' {
  import React from 'react';

  export interface ResizableProps {
    width: number;
    height: number;
    handle?: React.ReactElement;
    onResize?: (e: React.SyntheticEvent, data: { size: { width: number; height: number } }) => void;
    onResizeStart?: (e: React.SyntheticEvent) => void;
    onResizeStop?: (e: React.SyntheticEvent) => void;
    draggableOpts?: any;
    minConstraints?: [number, number];
    maxConstraints?: [number, number];
    lockAspectRatio?: boolean;
    axis?: 'both' | 'x' | 'y' | 'none';
    resizeHandles?: Array<'s' | 'w' | 'e' | 'n' | 'sw' | 'nw' | 'se' | 'ne'>;
    style?: React.CSSProperties;
    children: React.ReactElement;
  }

  export class Resizable extends React.Component<ResizableProps> {}
} 