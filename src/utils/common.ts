/**
 * 根据site获取对应的货币符号
 * @param site 站点代码
 * @returns 货币符号
 */
export const getSiteCurrency = (site: string): string => {
  const currencyMap: Record<string, string> = {
    // 北美
    'US': '$', // 美元
    'CA': 'C$', // 加拿大元
    'MX': 'MX$', // 墨西哥比索
    
    // 欧洲
    'UK': '£', // 英镑
    'GB': '£', // 英镑
    'EU': '€', // 欧元
    'DE': '€', // 德国欧元
    'FR': '€', // 法国欧元
    'IT': '€', // 意大利欧元
    'ES': '€', // 西班牙欧元
    
    // 亚洲
    'CN': '¥', // 人民币
    'JP': '¥', // 日元
    'KR': '₩', // 韩元
    'SG': 'S$', // 新加坡元
    'IN': '₹', // 印度卢比
    
    // 大洋洲
    'AU': 'A$', // 澳大利亚元
    'NZ': 'NZ$', // 新西兰元
    
    // 其他
    'BR': 'R$', // 巴西雷亚尔
    'RU': '₽', // 俄罗斯卢布
    'ZA': 'R', // 南非兰特
  };
  
  // 将site转换为大写，以适配不同大小写的输入
  const upperSite = site.toUpperCase();
  
  // 返回对应的货币符号，如果找不到则返回默认值'$'
  return currencyMap[upperSite] || '$';
};

export const sourceImageUrl = (url: string) => {
  return `https://ads-web.oss-cn-hangzhou.aliyuncs.com/images/${url}`;
};

// 添加处理亚马逊图片URL的函数
export const getResizedAmazonImageUrl = (url: string, size: number = 160): string => {
  if (!url) return '';

  // 尝试替换常见的亚马逊图片尺寸参数
  return url
    // .replace(/_(SL|SX|SY)\d+_/g, `_SL${size}_`)  // 替换 _SL80_, _SX80_, _SY80_ 等格式
    // .replace(/\._\w+(\d+)_\./g, `._SL${size}_`)  // 替换其他可能的格式
    // .replace(/\/(I|P)\/(\w+?)(\.|-)[_\w]*\./g, `/$1/$2.`) // 移除中间尺寸标记
    .replace(/\.(jpg|png|jpeg)/i, `._SL${size}_.$1`); // 添加尺寸标记
};

export const AiOptionList = {
  adTarget: { name: '广告目标', value: '广告目标：\n提高利润并稳定BSR排名，增加自然订单\nACoS控制在30%以内\n最大化大促期间的销量和曝光\n积累更多高质量客户评论' },
  // adParams: { name: '广告参数设置', value: '广告参数设置：\n日预算范围：$150~$200\n每周/日预算调整比例：5%~10%\n关键周/日预算调整比例：10%~20%\n每周/日竞价调整比例：5%~10%\n关键周/日竞价调整比例：10%~20%\n搜索结果顶部竞价调整比例：0%~50%\n详情页竞价调整比例：0%~50%\n其他位置竞价调整比例：0%~50%\n分时竞价调整比例：10%~20%' },
  competitor: { name: '对标竞品', value: '对标竞品：\n' }
};