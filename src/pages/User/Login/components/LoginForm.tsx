import {
  LockOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history, useModel, useIntl, FormattedMessage, Link } from '@umijs/max';
import { Alert, message, Space, theme, Form, Input, Button } from 'antd';
import React, { useState, useEffect } from 'react';
import { flushSync } from 'react-dom';
import { accountLogin, getDynamicCode } from '@/services/ibidder_api/user';
import { AuthUtils } from '@/utils/auth';
import styles from '../index.less';
import { registerPath } from '../../../../../config/routes';

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

interface LoginFormProps {
  onForgotPassword: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onForgotPassword }) => {
  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});
  const [type, setType] = useState<'wechat' | 'account'>('account');
  const [accountLoginMode, setAccountLoginMode] = useState<'password' | 'captcha'>('password');
  const [captchaCountDown, setCaptchaCountDown] = useState<number>(0);
  const { setInitialState } = useModel('@@initialState');
  const intl = useIntl();
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  useEffect(() => {
    if (process.env.REACT_APP_NO_AUTH) {
      history.push('/');
    }
  }, []);

  // 验证是否为邮箱格式
  const isEmail = (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  };

  // 验证是否为手机号格式（支持中国大陆手机号）
  const isPhone = (value: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(value);
  };

  const fetchUserInfo = async () => {
    // 从localStorage获取用户信息
    const storedUserInfo = AuthUtils.getUserInfo();
    
    if (storedUserInfo) {
      const userInfo = {
        name: AuthUtils.getUserDisplayName(),
        avatar: storedUserInfo.avatar || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
        userid: storedUserInfo.id.toString(),
        email: storedUserInfo.email || '',
        phone: storedUserInfo.phone || '',
        access: 'admin', // 根据user_type可以进一步判断权限
      };
      
      if (userInfo) {
        flushSync(() => {
          setInitialState((s) => ({
            ...s,
            currentUser: userInfo,
          }));
        });
      }
    }
  };

  const handleSubmit = async (values: { account: string; password?: string; captcha?: string }) => {
    try {
      if (type === 'wechat') {
        message.info(intl.formatMessage({ id: 'pages.login.wechatLogin.notImplemented', defaultMessage: "微信登录流程待实现" }));
        return;
      }

      const loginParams: API.AccountLoginParams = {};

      if (accountLoginMode === 'password') {
        if (!values.password) {
          message.error(intl.formatMessage({ id: 'pages.login.password.required', defaultMessage: '请输入密码！' }));
          return;
        }
        loginParams.password = values.password;
      } else { // captcha mode
        if (!values.captcha) {
          message.error(intl.formatMessage({ id: 'pages.login.captcha.required', defaultMessage: '请输入验证码！' }));
          return;
        }
        loginParams.dynamic_code = values.captcha;
      }

      if (isEmail(values.account)) {
        loginParams.email = values.account;
      } else if (isPhone(values.account)) {
        loginParams.phone = values.account;
      } else {
        loginParams.username = values.account;
      }

      const res = await accountLogin(loginParams) as any;
      if (res.code === 200) {
        const defaultLoginSuccessMessage = intl.formatMessage({
          id: 'pages.login.success',
          defaultMessage: '登录成功！',
        });
        message.success(defaultLoginSuccessMessage);
        
        // 存储tokens
        AuthUtils.handleLoginSuccess(res.data);
        
        await fetchUserInfo();
        const urlParams = new URL(window.location.href).searchParams;
        history.push(urlParams.get('redirect') || '/admin/listing');
        return;
      } else {
        message.error(res.message);
        setUserLoginState({
          status: 'error',
          type: 'account',
          currentAuthority: 'guest',
        });
      }
    } catch (error) {
      const defaultLoginFailureMessage = intl.formatMessage({
        id: 'pages.login.failure',
        defaultMessage: '登录失败，请重试！',
      });
      console.log(error);
      message.error(defaultLoginFailureMessage);
    }
  };

  const handleGetCaptcha = async () => {
    const accountValue = form.getFieldValue('account');
    if (!accountValue) {
      form.validateFields(['account']).catch(() => { });
      return;
    }

    try {
      const params: API.GetDynamicCodeParams = {};
      
      if (isEmail(accountValue)) {
        params.email = accountValue;
      } else if (isPhone(accountValue)) {
        params.phone = accountValue;
      } else {
        message.error(intl.formatMessage({ 
          id: 'pages.login.captcha.invalidAccount', 
          defaultMessage: '请输入有效的手机号或邮箱地址！' 
        }));
        return;
      }

      const res = await getDynamicCode(params) as any;
      
      if (res.code === 200) {
        message.success(intl.formatMessage({ 
          id: 'pages.login.getCaptcha.success', 
          defaultMessage: '验证码发送成功！' 
        }));
        
        setCaptchaCountDown(60);
        const timer = setInterval(() => {
          setCaptchaCountDown((prevCount) => {
            if (prevCount <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prevCount - 1;
          });
        }, 1000);
      } else {
        message.error(res.message || intl.formatMessage({ 
          id: 'pages.login.getCaptcha.failed', 
          defaultMessage: '验证码发送失败，请重试！' 
        }));
      }
    } catch (error) {
      console.error('获取验证码失败:', error);
      message.error(intl.formatMessage({ 
        id: 'pages.login.getCaptcha.failed', 
        defaultMessage: '验证码发送失败，请重试！' 
      }));
    }
  };

  const { status } = userLoginState;

  return (
    <div className={styles.loginFormContainer}>
      <div className={styles.loginHeader}>
        <div className={styles.loginTitle}>
          {intl.formatMessage({ id: 'pages.login.title', defaultMessage: "欢迎使用FlyPower" })}
        </div>
      </div>

      <div className={styles.loginTypeSwitcher}>
        <Space size={20}>
          {/* <a
            className={type === 'wechat' ? styles.activeLoginType : styles.inactiveLoginType}
            onClick={() => {
              setType('wechat');
              form.resetFields();
            }}
          >
            <FormattedMessage id="pages.login.wechatLogin" defaultMessage="微信登录" />
          </a> */}
          <a
            className={type === 'account' ? styles.activeLoginType : styles.inactiveLoginType}
            onClick={() => {
              setType('account');
              form.resetFields();
              setAccountLoginMode('password');
            }}
          >
            <FormattedMessage id="pages.login.accountLogin" defaultMessage="账号登录" />
          </a>
        </Space>
      </div>

      {status === 'error' && type === 'account' && (
        <LoginMessage
          content={intl.formatMessage({
            id: 'pages.login.accountLogin.errorMessage',
            defaultMessage: '账号或密码错误',
          })}
        />
      )}

      {type === 'wechat' && (
        <div className={styles.wechatLoginContainer}>
          <img
            src="/icons/wechat-qr-placeholder.svg"
            alt={intl.formatMessage({ id: 'pages.login.wechatLogin.scanToLogin', defaultMessage: '微信扫码登录' })}
            className={styles.wechatQrCode}
          />
          <div className={styles.wechatScanPrompt}>
            <FormattedMessage id="pages.login.wechatLogin.scanPrompt" defaultMessage="请使用微信扫码登录" />
          </div>
          <div className={`${styles.registerLinkContainer} ${styles.registerLinkWechat}`}>
            <FormattedMessage id="pages.login.noAccount" defaultMessage="还没有账号，" />
            <Link to={registerPath} style={{ color: token.colorPrimary }}>
              <FormattedMessage id="pages.login.registerNow" defaultMessage="去注册" />
            </Link>
          </div>
        </div>
      )}

      {type === 'account' && (
        <Form
          form={form}
          name="login"
          initialValues={{ remember: true }}
          onFinish={handleSubmit}
          className={styles.accountForm}
          style={{ width: '100%' }}
        >
          <Form.Item
            name="account"
            rules={[
              {
                required: true,
                message: intl.formatMessage({ id: 'pages.login.identifier.required', defaultMessage: '请输入手机号、邮箱或用户名！' }),
              },
            ]}
          >
            <Input
              prefix={<UserOutlined className={styles.prefixIcon} />}
              placeholder={intl.formatMessage({ id: 'pages.login.identifier.placeholder', defaultMessage: '请输入手机号、邮箱或用户名' })}
              size="large"
            />
          </Form.Item>

          {accountLoginMode === 'password' ? (
            <Form.Item
              name="password"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'pages.login.password.required', defaultMessage: '请输入密码！' }),
                },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className={styles.prefixIcon} />}
                placeholder={intl.formatMessage({ id: 'pages.login.password.placeholder', defaultMessage: '请输入密码' })}
                size="large"
              />
            </Form.Item>
          ) : (
            <Form.Item
              name="captcha"
              rules={[
                {
                  required: true,
                  message: intl.formatMessage({ id: 'pages.login.captcha.required', defaultMessage: '请输入验证码！' }),
                },
              ]}
            >
              <Space.Compact style={{ width: '100%' }}>
                <Input
                  placeholder={intl.formatMessage({ id: 'pages.login.captcha.placeholder', defaultMessage: '请输入验证码' })}
                  size="large"
                />
                <Button type="primary" size="large" className={styles.getCaptchaButton} disabled={captchaCountDown > 0} onClick={handleGetCaptcha}>
                  {captchaCountDown > 0 ? `${captchaCountDown}s` : intl.formatMessage({ id: 'pages.login.getCaptcha', defaultMessage: '获取验证码' })}
                </Button>
              </Space.Compact>
            </Form.Item>
          )}

          <div className={styles.accountLinks}>
            {accountLoginMode === 'password' ? (
              <a
                onClick={() => {
                  setAccountLoginMode('captcha');
                  form.resetFields(['password', 'captcha']);
                }}
                style={{ color: token.colorPrimary }}
              >
                {/* <FormattedMessage id="pages.login.captchaLoginLink" defaultMessage="验证码登录" /> */}
              </a>
            ) : (
              <a
                onClick={() => {
                  setAccountLoginMode('password');
                  form.resetFields(['password', 'captcha']);
                }}
                style={{ color: token.colorPrimary }}
              >
                <FormattedMessage id="pages.login.passwordLoginLink" defaultMessage="密码登录" />
              </a>
            )}
            {accountLoginMode === 'password' && (
              <a 
                onClick={onForgotPassword}
                style={{ color: token.colorTextSecondary, cursor: 'pointer' }}
              >
                {/* <FormattedMessage id="pages.login.forgotPassword" defaultMessage="忘记密码" /> */}
              </a>
            )}
          </div>

          <Form.Item>
            <Button type="primary" htmlType="submit" className={styles.loginFormButton} size="large">
              {intl.formatMessage({ id: 'pages.login.submit', defaultMessage: '登录' })}
            </Button>
          </Form.Item>

          <div className={styles.registerLinkContainer}>
            <FormattedMessage id="pages.login.noAccount" defaultMessage="还没有账号，" />
            <Link to={registerPath} style={{ color: token.colorPrimary }}>
              <FormattedMessage id="pages.login.registerNow" defaultMessage="去注册" />
            </Link>
          </div>
        </Form>
      )}
    </div>
  );
};

export default LoginForm; 