import {
  Alert,
  Form,
  Input,
  But<PERSON>,
  Row,
  Col,
  Checkbox
} from 'antd';
import React, { useState } from 'react';
import { getRegistrationNotice } from '@/services/ibidder_api/user';
import styles from '../index.less';

const RegisterMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

interface RegisterFormProps {
  className?: string;
  onSuccess?: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  className,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [selectedCompanyType, setSelectedCompanyType] = useState<string>('');
  const [selectedAdSpend, setSelectedAdSpend] = useState<string>('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submittedValues, setSubmittedValues] = useState<any>(null);
  const [resultCode, setResultCode] = useState(0)

  // 处理演示请求提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      setErrorMessage('');
      delete values.agreement;

      // 调用getRegistrationNotice接口
      const result = await getRegistrationNotice(values);
      setResultCode(result.code)

      setSubmittedValues(values);
      setIsSubmitted(true);

    } catch (error: any) {
      setErrorMessage(error.message || '预约失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  if (isSubmitted && submittedValues) {
    const displayName = submittedValues.name || submittedValues.workEmail.split('@')[0];
    if (resultCode === 200) {
      return (
        <div className={`${styles.registerFormContainer} ${className || ''}`} style={{ textAlign: 'left' }}>
          <div className={styles.registerHeader}>
            <div className={styles.registerTitle}>预约成功</div>
          </div>
          <div style={{ padding: '20px', border: '1px solid #e8e8e8', borderRadius: '8px', backgroundColor: '#fafafa' }}>
            <h3 style={{ fontWeight: 'bold' }}>尊敬的 {displayName},</h3>
            <p>感谢您申请 FlyPower.AI 产品演示。我们已经收到了您的请求，并将尽快安排专人与您联系。</p>
            <p>以下是您提交的信息摘要：</p>
            <div style={{ background: '#fff', padding: '15px', borderRadius: '4px', border: '1px solid #e8e8e8', marginTop: '15px' }}>
              <p><strong>姓名:</strong> {submittedValues.name || '未填写'}</p>
              <p><strong>工作邮箱:</strong> {submittedValues.workEmail}</p>
              <p><strong>电话号码:</strong> {submittedValues.phoneNumber || '未填写'}</p>
              <p><strong>公司名称:</strong> {submittedValues.companyName || '未填写'}</p>
              <p><strong>公司类型:</strong> {submittedValues.companyType}</p>
              <p><strong>预估月度广告支出:</strong> {submittedValues.monthlyAdSpend}</p>
            </div>
            <p style={{ marginTop: '15px' }}>我们的团队将通过您提供的邮箱 <strong>({submittedValues.workEmail})</strong> 或电话与您取得联系，请您保持通讯畅通。</p>
            <p>再次感谢您的信任与支持！</p>
          </div>
        </div>
      );
    } else {
      return (
        <div className={`${styles.registerFormContainer} ${className || ''}`} style={{ textAlign: 'left' }}>
          <div className={styles.registerHeader}>
            <div className={styles.registerTitle}>预约失败</div>
          </div>
          <div style={{ padding: '20px', border: '1px solid #e8e8e8', borderRadius: '8px', backgroundColor: '#fafafa' }}>
            <h3 style={{ fontWeight: 'bold' }}>尊敬的 {displayName},</h3>
            <p>很抱歉，您的演示请求提交失败。</p>
            <p>请您稍后再试，或通过 <a href="mailto:<EMAIL>"><EMAIL></a> 与我们取得联系。</p>
            <p>再次感谢您的信任与支持！</p>
          </div>
        </div>
      );
    }
  }

  return (
    <div className={`${styles.registerFormContainer} ${className || ''}`}>
      <div className={styles.registerHeader}>
        <div className={styles.registerTitle}>申请演示</div>
      </div>

      {errorMessage && <RegisterMessage content={errorMessage} />}

      <Form
        form={form}
        name="demo-request"
        onFinish={handleSubmit}
        autoComplete="off"
        size="large"
        className={styles.registerForm}
        layout="vertical"
      >
        {/* 姓名 */}
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="姓名"
              name="name"
              rules={[
                { required: true, message: '请输入您的姓名' }
              ]}
            >
              <Input placeholder="" />
            </Form.Item>
          </Col>
        </Row>

        {/* 工作邮箱和电话 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="工作邮箱"
              name="workEmail"
              rules={[
                { required: true, message: '请输入您的工作邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input placeholder="" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="手机号码"
              name="phoneNumber"
              rules={[
                { required: true, message: '请输入您的手机号码' },
                {
                  pattern: /^1\d{10}$/,
                  message: '请输入以1开头的11位中国大陆手机号码'
                }
              ]}
              validateTrigger="onBlur"
            >
              <Input placeholder="" maxLength={11} />
            </Form.Item>
          </Col>
        </Row>

        {/* 公司名称 */}
        <Form.Item
          label="公司名称"
          name="companyName"
          rules={[
            { required: true, message: '请输入您的公司名称' }
          ]}
        >
          <Input placeholder="" />
        </Form.Item>

        {/* 公司类型 */}
        <Form.Item
          label="公司类型"
          name="companyType"
          rules={[
            { required: true, message: '请选择您的公司类型' }
          ]}
        >
          <div className={styles.companyTypeButtons}>
            <Button
              key={`company-代理商-${selectedCompanyType}`}
              type={selectedCompanyType === '代理商' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedCompanyType('代理商');
                form.setFieldsValue({ companyType: '代理商' });
                form.validateFields(['companyType']);
              }}
              className={styles.typeButton}
            >
              代理商
            </Button>
            <Button
              key={`company-品牌方-${selectedCompanyType}`}
              type={selectedCompanyType === '品牌方' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedCompanyType('品牌方');
                form.setFieldsValue({ companyType: '品牌方' });
                form.validateFields(['companyType']);
              }}
              className={styles.typeButton}
            >
              品牌方
            </Button>
            <Button
              key={`company-卖家-${selectedCompanyType}`}
              type={selectedCompanyType === '卖家' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedCompanyType('卖家');
                form.setFieldsValue({ companyType: '卖家' });
                form.validateFields(['companyType']);
              }}
              className={styles.typeButton}
            >
              卖家
            </Button>
            <Button
              key={`company-其他-${selectedCompanyType}`}
              type={selectedCompanyType === '其他' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedCompanyType('其他');
                form.setFieldsValue({ companyType: '其他' });
                form.validateFields(['companyType']);
              }}
              className={styles.typeButton}
            >
              其他
            </Button>
          </div>
        </Form.Item>

        {/* 月度广告支出 */}
        <Form.Item
          label="您的预估月度广告支出是多少？"
          name="monthlyAdSpend"
          rules={[
            { required: true, message: '请选择您的预估月度广告支出' }
          ]}
        >
          <div className={styles.adSpendButtons}>
            <Button
              key={`spend-少于1万美元-${selectedAdSpend}`}
              type={selectedAdSpend === '少于1万美元' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedAdSpend('少于1万美元');
                form.setFieldsValue({ monthlyAdSpend: '少于1万美元' });
                form.validateFields(['monthlyAdSpend']);
              }}
              className={styles.spendButton}
            >
              少于1万美元
            </Button>
            <Button
              key={`spend-1万-5万美元-${selectedAdSpend}`}
              type={selectedAdSpend === '1万-5万美元' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedAdSpend('1万-5万美元');
                form.setFieldsValue({ monthlyAdSpend: '1万-5万美元' });
                form.validateFields(['monthlyAdSpend']);
              }}
              className={styles.spendButton}
            >
              1万-5万美元
            </Button>
            <Button
              key={`spend-5万-8万美元-${selectedAdSpend}`}
              type={selectedAdSpend === '5万-8万美元' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedAdSpend('5万-8万美元');
                form.setFieldsValue({ monthlyAdSpend: '5万-8万美元' });
                form.validateFields(['monthlyAdSpend']);
              }}
              className={styles.spendButton}
            >
              5万-8万美元
            </Button>
            <Button
              key={`spend-8万-25万美元-${selectedAdSpend}`}
              type={selectedAdSpend === '8万-25万美元' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedAdSpend('8万-25万美元');
                form.setFieldsValue({ monthlyAdSpend: '8万-25万美元' });
                form.validateFields(['monthlyAdSpend']);
              }}
              className={styles.spendButton}
            >
              8万-25万美元
            </Button>
            <Button
              key={`spend-25万-50万美元-${selectedAdSpend}`}
              type={selectedAdSpend === '25万-50万美元' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedAdSpend('25万-50万美元');
                form.setFieldsValue({ monthlyAdSpend: '25万-50万美元' });
                form.validateFields(['monthlyAdSpend']);
              }}
              className={styles.spendButton}
            >
              25万-50万美元
            </Button>
            <Button
              key={`spend-50万-100万美元-${selectedAdSpend}`}
              type={selectedAdSpend === '50万-100万美元' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedAdSpend('50万-100万美元');
                form.setFieldsValue({ monthlyAdSpend: '50万-100万美元' });
                form.validateFields(['monthlyAdSpend']);
              }}
              className={styles.spendButton}
            >
              50万-100万美元
            </Button>
            <Button
              key={`spend-超过100万美元-${selectedAdSpend}`}
              type={selectedAdSpend === '超过100万美元' ? 'primary' : 'default'}
              onClick={() => {
                setSelectedAdSpend('超过100万美元');
                form.setFieldsValue({ monthlyAdSpend: '超过100万美元' });
                form.validateFields(['monthlyAdSpend']);
              }}
              className={styles.spendButton}
            >
              超过100万美元
            </Button>
          </div>
        </Form.Item>

        {/* 同意条款 */}
        <Form.Item
          name="agreement"
          valuePropName="checked"
          rules={[
            {
              validator: (_, value) =>
                value ? Promise.resolve() : Promise.reject(new Error('请勾选同意条款')),
            },
          ]}
        >
          <Checkbox>
            我已阅读并同意
            <a
              href="/pdf/termsofuse.pdf"
              target="_blank"
              rel="noopener noreferrer"
              className={'color-primary'}
            >
              《使用条款》
            </a>
            及
            <a
              href="/pdf/privacystatement.pdf"
              target="_blank"
              rel="noopener noreferrer"
              className={'color-primary'}
            >
              《隐私声明》
            </a>
          </Checkbox>
        </Form.Item>

        {/* 提交按钮 */}
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className={styles.demoButton}
            block
          >
            申请演示
          </Button>
        </Form.Item>

        {/* 登录链接 */}
        <div style={{ textAlign: 'center' }}>
          <span style={{ color: '#8c8c8c' }}>已有账号？</span>
          <a href="/login" className='color-primary'>前往登录</a>
        </div>
      </Form>
    </div>
  );
};

export default RegisterForm;
