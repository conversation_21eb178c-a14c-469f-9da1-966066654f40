import React, { useState, useEffect } from 'react';
import { Tabs, <PERSON>, But<PERSON>, Pagination, Typography, Spin, Badge, Space, Card, Row, Col } from 'antd';
import styles from './style.less';
import { getMessages } from '@/services/ibidder_api/user';
import { useSearchParams, useModel } from '@umijs/max';
import { getAvatarByType } from './../bus';
import dayjs from 'dayjs';
import { getDayOfWeek } from '@/utils/bus';

// 导入新的高阶弹框组件
import {
  DayStrategyModal,
  WeekStrategyModal,
  MonthReportModal
} from '../Modals';

const { TabPane } = Tabs;
const { Title } = Typography;

const job_idMap = {
  'market_report_month': '下月市场分析报告',
  'ads_strategy_week': '下周广告投放策略',
  'ads_strategy_day': '明天广告投放策略',
}

const roleMap: { [key: string]: string } = {
  strategyAgent: '广告策略师',
  operAgent: '广告优化师',
  marketAgent: '市场分析师',
};

const roleDescMap: { [key: string]: string } = {
  strategyAgent: '广告增长引擎',
  operAgent: 'ROI操盘手',
  marketAgent: '市场雷达',
};

const Todo: React.FC = () => {
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { unreadCount, updateUnreadCount } = useModel('unreadCount');

  const [activeTab, setActiveTab] = useState('0');
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  // 分别管理不同类型的弹框状态
  const [monthReportModal, setMonthReportModal] = useState<{
    open: boolean;
    data: any;
  }>({
    open: false,
    data: null
  });

  const [weekStrategyModal, setWeekStrategyModal] = useState<{
    open: boolean;
    data: any;
  }>({
    open: false,
    data: null
  });

  const [dayStrategyModal, setDayStrategyModal] = useState<{
    open: boolean;
    data: any;
  }>({
    open: false,
    data: null
  });

  const fetchData = (isUnreadCountFetch = false) => {
    setLoading(true);
    getMessages({
      is_read: isUnreadCountFetch ? 0 : parseInt(activeTab),
      message_type: 5,
      page_no: pageNo,
      page_size: pageSize,
      parent_asin: asin,
      profile_id: profile_id,
    }).then(res => {
      const result = res.data;
      if (activeTab === '0') {
        updateUnreadCount(result.total || 0);
      }
      setData(result.list || []);
      setTotal(result.total || 0);
    }).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    fetchData();
  }, [activeTab, pageNo, pageSize]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setPageNo(1);
  };

  const handlePaginationChange = (page: number, size: number) => {
    setPageNo(page);
    setPageSize(size);
  };

  // 关闭月报告弹框
  const closeMonthReportModal = () => {
    setMonthReportModal({
      open: false,
      data: null
    });
  };

  // 关闭周策略弹框
  const closeWeekStrategyModal = () => {
    setWeekStrategyModal({
      open: false,
      data: null
    });
  };

  // 关闭日策略弹框
  const closeDayStrategyModal = () => {
    setDayStrategyModal({
      open: false,
      data: null
    });
  };

  // 获取展示时间
  const getDisplayDate = (data: any) => {
    const { job_id, report_data } = data;
    const realData = report_data?.[job_id];
    let displayDate = ''
    if (!realData) return displayDate
    switch (job_id) {
      case 'ads_strategy_day':
        displayDate = realData.date + getDayOfWeek(realData.date)
        break;
      case 'ads_strategy_week':
        displayDate = realData.start_date + '~' + realData.end_date
        break;
      case 'market_report_month':
        displayDate = realData.start_date + '~' + realData.end_date
        break;
      default:
        displayDate = ''
    }
    return displayDate ? `（${displayDate}）` : ''
  }

  // 获取确认人显示名称
  const getReviewerDisplayName = (reviewerName: string) => {
    return reviewerName === 'sys' ? '超时系统自动审核' : reviewerName;
  }

  const showModal = (item: any) => {
    const reportData = item.extra_data;
    const job_id = reportData.job_id;

    // 根据报告类型打开对应的弹框
    if (job_id === 'market_report_month') {
      setMonthReportModal({
        open: true,
        data: item.extra_data
      });
    } else if (job_id === 'ads_strategy_week') {
      setWeekStrategyModal({
        open: true,
        data: item.extra_data
      });
    } else if (job_id === 'ads_strategy_day') {
      setDayStrategyModal({
        open: true,
        data: item.extra_data
      });
    }
  };

  return (
    <div>
      <Title level={3} style={{ marginTop: '10px' }}>待办事项</Title>
      <Card className="card">
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab={<>待确认{unreadCount > 0 ? <Badge count={unreadCount} style={{ marginLeft: 8 }} /> : ''}</>} key="0" />
          <TabPane tab="已确认" key="1" />
        </Tabs>
        {/* <Flex justify="flex-end" align="center" style={{ marginTop: '1em' }}>
        <RangePicker
          onChange={(dates) => {
            setDateRange(dates as [dayjs.Dayjs | null, dayjs.Dayjs | null]);
            setPageNo(1);
          }}
        />
      </Flex> */}
        <Spin spinning={loading}>
          <List
            itemLayout="horizontal"
            dataSource={data}
            style={{ paddingTop: '1em' }}
            renderItem={item => (
              <List.Item
                key={item.id}
                actions={activeTab === '0' ? [
                  <Button key={`review-btn-${item.id}`} type="primary" ghost onClick={() => showModal(item)}>去审核</Button>] : []}
              >
                <Row style={{ width: '100%' }} justify="space-between" align="middle" gutter={16}>
                  <Col flex={1} >
                    <Space style={{ paddingLeft: '1em' }}>
                      <div style={{display: 'flex', alignItems: 'center'}}>
                        {getAvatarByType(item.extra_data.role)}
                        <span className={styles.todoRole}>
                          <span className={styles.aiWorkType}>{roleMap[item.extra_data.role]}</span>
                          <span className={styles.aiWorkDesc}>{roleDescMap[item.extra_data.role]}</span>
                        </span>
                      </div>
                      <span>
                        {activeTab === '0' ? '请您审核' : '已确认'}
                        <span
                          style={{ color: '#4C6FFF', cursor: 'pointer' }}
                          onClick={() => showModal(item)}
                        >
                          {job_idMap[item.extra_data.job_id as keyof typeof job_idMap]}
                        </span>
                        {getDisplayDate(item.extra_data)}
                      </span>
                    </Space>
                  </Col>

                  <Col style={{width: 250}}>
                    {activeTab === '1' && item.reviewer_name && (
                      <span>
                        {getReviewerDisplayName(item.reviewer_name)}
                      </span>
                    )}
                  </Col>
                  <Col style={{width: 220}}>
                    <span>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}（北京）</span>
                  </Col>
                </Row>
              </List.Item>
            )}
          />
        </Spin>
        {total > 0 &&
          <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              current={pageNo}
              pageSize={pageSize}
              total={total}
              onChange={handlePaginationChange}
              showSizeChanger showQuickJumper
              pageSizeOptions={['5', '10', '20', '50']}
              showTotal={(total) => `共 ${total} 条`}
            />
          </div>}

      </Card>

      {/* 使用新的高阶弹框组件 */}
      {/* 月报告弹框 */}
      <MonthReportModal
        open={monthReportModal.open}
        onCancel={closeMonthReportModal}
        job_id={monthReportModal.data?.job_id || ''}
        current_time={monthReportModal.data?.current_time || ''}
        type="month"
        onSuccess={() => {
          closeMonthReportModal();
          if(activeTab === '0') fetchData(true);
        }}
      />

      {/* 周策略弹框 */}
      <WeekStrategyModal
        open={weekStrategyModal.open}
        onCancel={closeWeekStrategyModal}
        job_id={weekStrategyModal.data?.job_id || ''}
        current_time={weekStrategyModal.data?.current_time || ''}
        date={weekStrategyModal.data?.report_data?.ads_strategy_week?.start_date}
        isCompleteStrategy={true}
        onSuccess={() => {
          closeWeekStrategyModal();
          if(activeTab === '0') fetchData(true);
        }}
      />

      {/* 日策略弹框 */}
      <DayStrategyModal
        open={dayStrategyModal.open}
        onCancel={closeDayStrategyModal}
        job_id={dayStrategyModal.data?.job_id || ''}
        current_time={dayStrategyModal.data?.current_time || ''}
        date={dayStrategyModal.data?.report_data?.ads_strategy_day?.date}
        onSuccess={() => {
          closeDayStrategyModal();
          if(activeTab === '0') fetchData(true);
        }}
      />
    </div>
  );
};
export default Todo;
