import React, { useState } from 'react';
import { Button, Input, Modal, message } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { submitFeedback } from '@/services/ibidder_api/operation';

interface FeedbackButtonProps {
  /** 是否禁用按钮 */
  disabled?: boolean;
  /** 问题反馈相关参数 */
  feedbackParams?: {
    parent_asin: string;
    profile_id: string;
    job_id: string;
    es_id: string;
    current_time: string;
  };
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const FeedbackButton: React.FC<FeedbackButtonProps> = ({
  disabled = false,
  feedbackParams,
  style,
}) => {
  // 问题反馈相关状态
  const [feedbackModalVisible, setFeedbackModalVisible] = useState<boolean>(false);
  const [feedbackContent, setFeedbackContent] = useState<string>('');

  // 问题反馈提交请求
  const { run: submitFeedbackRequest, loading: feedbackLoading } = useRequest(submitFeedback, {
    manual: true,
    onSuccess: () => {
      message.success('反馈提交成功，感谢您的反馈！');
      setFeedbackModalVisible(false);
      setFeedbackContent('');
    },
    onError: (error) => {
      message.error('反馈提交失败，请重试');
      console.error('反馈提交失败:', error);
    },
  });

  // 打开问题反馈弹窗
  const handleOpenFeedback = () => {
    setFeedbackModalVisible(true);
  };

  // 关闭问题反馈弹窗
  const handleCloseFeedback = () => {
    setFeedbackModalVisible(false);
    setFeedbackContent('');
  };

  // 提交问题反馈
  const handleSubmitFeedback = () => {
    if (!feedbackContent.trim()) {
      message.error('请输入反馈内容');
      return;
    }

    if (!feedbackParams) {
      message.error('缺少必要的参数，无法提交反馈');
      return;
    }

    // 从es_id中提取版本号
    const ver = feedbackParams.es_id ? feedbackParams.es_id.split('_')[1] || '1' : '1';

    const params = {
      parent_asin: feedbackParams.parent_asin,
      profile_id: feedbackParams.profile_id,
      job_id: feedbackParams.job_id,
      ver: ver,
      current_time: feedbackParams.current_time,
      content: feedbackContent.trim(),
    };
    submitFeedbackRequest(params);
  };

  return (
    <>
      <Button
      className='btn-primary'
        onClick={handleOpenFeedback}
        disabled={disabled}
        style={style}
        icon={<ExclamationCircleOutlined />}
      >
        问题反馈
      </Button>

      {/* 问题反馈弹窗 */}
      <Modal
        title="问题反馈"
        open={feedbackModalVisible}
        onCancel={handleCloseFeedback}
        footer={[
          <Button key="cancel" onClick={handleCloseFeedback}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={feedbackLoading}
            onClick={handleSubmitFeedback}
          >
            提交
          </Button>,
        ]}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <Input.TextArea
            value={feedbackContent}
            onChange={(e) => setFeedbackContent(e.target.value)}
            placeholder="请在此输入您需要反馈的错误内容，我们将及时排查问题......"
            rows={8}
          />
        </div>
      </Modal>
    </>
  );
};

export default FeedbackButton;
