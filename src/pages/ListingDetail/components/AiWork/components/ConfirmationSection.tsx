import React, { useState } from 'react';
import { Button, Input } from 'antd';
import { WarningOutlined } from '@ant-design/icons';

interface ConfirmationSectionProps {
  /** 当前文本区域的值 */
  value?: string;
  /** 文本区域变化处理函数 */
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  /** 审核回调函数 */
  onApprove?: () => void;
  /** 修改回调函数 */
  onModify?: () => void;
  /** 是否禁用按钮 */
  disabled?: boolean;
  /** 文本区域最小行数 */
  minRows?: number;
  /** 文本区域最大行数 */
  maxRows?: number;
  /** 文本区域最大宽度 */
  maxWidth?: number | string;
  /** 按钮大小 */
  buttonSize?: 'small' | 'middle' | 'large';
  /** 文本区域提示语 */
  placeholder: string;
  /** 是否为审核模式，为true时不显示审核按钮 */
  report_status: boolean;
}

const ConfirmationSection: React.FC<ConfirmationSectionProps> = ({
  value = '',
  onChange,
  onApprove,
  onModify,
  disabled = false,
  minRows = 5,
  maxRows = 8,
  maxWidth = '100%',
  buttonSize = 'large',
  placeholder = '请输入修改意见',
  report_status = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    // 清除输入内容
    if (onChange) {
      onChange({ target: { value: '' } } as React.ChangeEvent<HTMLTextAreaElement>);
    }
    // 不调用 onCancel，避免关闭窗口
  };

  const handleModifyClick = () => {
    if (onModify) {
      onModify();
    }
  };

  const handleApproveClick = () => {
    if (onApprove) {
      onApprove();
    }
  };

  const isSubmitDisabled = disabled || !value || value.trim().length === 0;

  return (
    <div className='confirmBtn'>
      {!isEditing ? (
        // 初始状态：显示修改和审核按钮
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
          <Button
            size={buttonSize}
            style={{ width: '100px' }}
            onClick={handleEditClick}
            disabled={disabled}
          >
            修改
          </Button>
          {!report_status && (
            <Button
              type="primary"
              size={buttonSize}
              style={{ width: '200px' }}
              onClick={handleApproveClick}
              disabled={disabled}
            >
              审核
            </Button>
          )}
        </div>
      ) : (
        // 编辑状态：显示输入框和取消/提交按钮
        <>
          <div >
            <Input.TextArea
              autoSize={{ minRows, maxRows }}
              style={{ width: '100%', maxWidth }}
              placeholder={placeholder}
              value={value}
              onChange={onChange}
              autoFocus
            />
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', gap: '20px' }}>
            <div style={{ display: 'flex', flex: 1, alignItems: 'center', gap: '4px', color: '#D80027' }}>
              <WarningOutlined style={{fontSize: '16px'}} />
              <div style={{lineHeight: '1.3'}}>
                除非需要大幅度修改，一般不建议修改 Campaign 的预算和竞价、分时竞价、广告位竞价，AI 会根据实际投放表现动态调整。
                <br />
                若需调整广告位竞价，修改时需要带上%，例如：竞价调整为50%
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <Button
                size={buttonSize}
                style={{ width: '100px' }}
                onClick={handleCancelClick}
                disabled={disabled}
              >
                取消
              </Button>
              <Button
                type="primary"
                size={buttonSize}
                style={{
                  width: '200px',
                  backgroundColor: isSubmitDisabled ? '#ffccc7' : '#ff4d4f',
                  borderColor: isSubmitDisabled ? '#ffccc7' : '#ff4d4f',
                  color: '#fff'
                }}
                onClick={handleModifyClick}
                disabled={isSubmitDisabled}
              >
                提交修改意见
              </Button>
            </div>
            
          </div>
        </>
      )}
    </div>
  );
};

export default ConfirmationSection;
