import React from 'react';
import { Row, <PERSON>, Card, Statistic, Typography } from 'antd';
import { RiseOutlined, FallOutlined } from '@ant-design/icons';
import { processNumberOrString } from '@/utils/bus';

const { Text } = Typography;

interface ExpectedResultsProps {
  spend: {
    typical: number;
    min: number;
    max: number;
    change_from_last_week: string;
  };
  sales: {
    typical: number;
    min: number;
    max: number;
    change_from_last_week: string;
  };
  acos: {
    typical: number;
    min: number;
    max: number;
    change_from_last_week: string;
  };
  cvr: {
    typical: number;
    min: number;
    max: number;
    change_from_last_week: string;
  };
  real_ads_result: Strategy.Real_ads_result | undefined;
}

const ExpectedResults: React.FC<ExpectedResultsProps> = ({
  spend,
  sales,
  acos,
  cvr,
  real_ads_result
}) => {
  const renderChangeIndicator = (change: string) => {
    const changeValue = parseFloat(change);
    if (changeValue > 0) {
      return (
        <Text type="danger" style={{ fontSize: '14px' }}>
          较上周: {change}
          <RiseOutlined style={{ marginLeft: 4 }} />
        </Text>
      );
    } else if (changeValue < 0) {
      return (
        <Text type="success" style={{ fontSize: '14px' }}>
          较上周: {change}
          <FallOutlined style={{ marginLeft: 4 }} />
        </Text>
      );
    } else {
      return (
        <Text style={{ fontSize: '14px', color: '#666' }}>
          较上周: {change}
        </Text>
      );
    }
  };

  return (
    <Row gutter={16}>
      <Col span={6}>
        <Card className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期花费</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={spend.typical}
            prefix="$"
            precision={2}
          />
          <div style={{ marginTop: 8 }}>
            <Text style={{ fontSize: '14px', color: '#666' }}>
              ${spend.min} ~ ${spend.max}
            </Text>
            <br />
            {renderChangeIndicator(spend.change_from_last_week)}
          </div>
          {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 12, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前花费</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.spend !== null ? "$" + real_ads_result.spend : '-'}
                </Text>
              </div>
            )
          }
        </Card>
      </Col>
      <Col span={6}>
        <Card className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期销售额</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={sales.typical}
            prefix="$"
            precision={2}
          />
          <div style={{ marginTop: 8 }}>
            <Text style={{ fontSize: '14px', color: '#666' }}>
              ${sales.min} ~ ${sales.max}
            </Text>
            <br />
            {renderChangeIndicator(sales.change_from_last_week)}
          </div>
          {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 12, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前销售额</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.sales !== null ? "$" + real_ads_result.sales.toFixed(2) : '-'}
                </Text>
              </div>
            )
          }
        </Card>
      </Col>
      <Col span={6}>
        <Card className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期ACOS</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={processNumberOrString(acos.typical)}
            suffix="%"
          />
          <div style={{ marginTop: 8 }}>
            <Text style={{ fontSize: '14px', color: '#666' }}>
              {(acos.min * 100).toFixed(2)}% ~ {(acos.max * 100).toFixed(2)}%
            </Text>
            <br />
            {renderChangeIndicator(acos.change_from_last_week)}
          </div>
          {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 12, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前ACOS</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.acos !== null ? (real_ads_result.acos).toFixed(2) : '-'}%
                </Text>
              </div>
            )
          }
        </Card>
      </Col>
      <Col span={6}>
        <Card className="card" style={{ minHeight: 130 }}>
          <Statistic
            title={<div style={{ fontSize: '14px', color: '#8c8c8c' }}>预期转化率</div>}
            valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
            value={processNumberOrString(cvr.typical)}
            suffix="%"
          />
          <div style={{ marginTop: 8 }}>
            <Text style={{ fontSize: '14px', color: '#666' }}>
              {(cvr.min * 100).toFixed(2)}% ~ {(cvr.max * 100).toFixed(2)}%
            </Text>
            <br />
            {renderChangeIndicator(cvr.change_from_last_week)}
          </div>
          {
            real_ads_result && (
              <div style={{ marginTop: 14, paddingTop: 16, borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>当前转化率</Text>
                <Text style={{ fontSize: '14px', color: '#333', fontWeight: 'bold' }}>
                  {real_ads_result.cvr !== null ? (real_ads_result.cvr).toFixed(2) : '-'}%
                </Text>
              </div>
            )
          }
        </Card>
      </Col>
    </Row>
  );
};

export default ExpectedResults;