import DayStrategyCatContent from '../AiWork/components/dayStrategyCat';
import withModalWrapper, { ModalWrapperProps, ContentComponentProps } from '../ModalWrapper';
import { DayStrategyCatModalData } from '@/models/globalModals';

// 组合props类型
type DayStrategyCatModalProps = Omit<DayStrategyCatModalData, keyof ContentComponentProps> & 
  ModalWrapperProps & 
  ContentComponentProps;

// 使用高阶组件包装内容组件
const DayStrategyCatModal = withModalWrapper(DayStrategyCatContent);

export default DayStrategyCatModal;
export type { DayStrategyCatModalProps }; 