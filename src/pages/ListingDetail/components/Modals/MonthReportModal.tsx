import withModalWrapper, { ModalWrapperProps, ContentComponentProps } from '../ModalWrapper';
import WeekMonthAnalysisContent from '../AiWork/components/weekAnalysis';
import { WeekMonthReportModalData } from '@/models/globalModals';

// 组合props类型
type MonthReportModalProps = Omit<WeekMonthReportModalData, keyof ContentComponentProps> & 
  ModalWrapperProps & 
  ContentComponentProps & {
    type: 'month';
  };

// 使用高阶组件包装内容组件
const MonthReportModal = withModalWrapper(WeekMonthAnalysisContent);

export default MonthReportModal;
export type { MonthReportModalProps }; 