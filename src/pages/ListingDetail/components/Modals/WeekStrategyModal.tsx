import withModalWrapper, { ModalWrapperProps, ContentComponentProps } from '../ModalWrapper';
import WeekStrategyContent from '../AiWork/components/weekStrategy';
import { WeekStrategyModalData } from '@/models/globalModals';

// 组合props类型
type WeekStrategyModalProps = Omit<WeekStrategyModalData, keyof ContentComponentProps> & 
  ModalWrapperProps & 
  ContentComponentProps;

// 使用高阶组件包装内容组件
const WeekStrategyModal = withModalWrapper(WeekStrategyContent);

export default WeekStrategyModal;
export type { WeekStrategyModalProps }; 