import React, { useState, useCallback } from 'react';
import { Modal, Typography } from 'antd';

const { Title } = Typography;

/**
 * 弹框包装器的属性接口
 * 定义了弹框的基本配置选项
 */
export interface ModalWrapperProps {
  /** 控制弹框的显示/隐藏状态 */
  open: boolean;
  /** 弹框关闭时的回调函数 */
  onCancel: () => void;
  /** 弹框宽度，默认为 "90%" */
  width?: string | number;
  /** 弹框的 CSS 类名，默认为 'report-modal' */
  className?: string;
}

/**
 * 内容组件的属性接口
 * 定义了内容组件需要实现的标准回调函数
 */
export interface ContentComponentProps {
  /** 标题变更回调，内容组件通过此函数向弹框传递标题 */
  onTitleChange?: (title: string) => void;
  /** 取消操作回调，支持任意参数 */
  onCancel?: (...args: any[]) => void;
  /** 成功操作回调，支持任意参数 */
  onSuccess?: (...args: any[]) => void;
}

/**
 * 高阶组件：弹框包装器
 * 
 * 功能说明：
 * 1. 将内容组件包装在 Modal 中，提供统一的弹框样式和行为
 * 2. 自动管理弹框标题，内容组件通过 onTitleChange 回调设置标题
 * 3. 统一处理弹框的打开/关闭逻辑
 * 4. 支持内容组件的独立使用和弹框模式使用
 * 
 * @template T - 内容组件的属性类型，必须继承 ContentComponentProps
 * @param WrappedComponent - 被包装的内容组件
 * @returns 包装后的弹框组件
 * 
 * 使用示例：
 * ```tsx
 * // 创建弹框组件
 * const DayStrategyModal = withModalWrapper(DayStrategyContent);
 * 
 * // 使用弹框组件
 * <DayStrategyModal
 *   open={modalOpen}
 *   onCancel={() => setModalOpen(false)}
 *   job_id="ads_strategy_day"
 *   current_time="2024-01-15"
 *   date="2024-01-15"
 * />
 * ```
 */
function withModalWrapper<T extends ContentComponentProps>(
  WrappedComponent: React.ComponentType<T>
) {
  /**
   * 包装后的弹框组件
   * 
   * @param props - 组件属性，包含 ModalWrapperProps 和内容组件的属性
   * @returns 渲染的弹框组件
   */
  return function ModalWrappedComponent(
    // 组件属性类型：
    // 1. Omit<T, keyof ContentComponentProps>：去除内容组件自身的回调（如 onTitleChange/onCancel/onSuccess），避免与弹框外部传入的冲突
    // 2. ModalWrapperProps：弹框包装器需要的通用属性（如 open、onCancel、width 等）
    // 3. Partial<ContentComponentProps>：允许外部传入部分内容组件的回调（如 onTitleChange/onCancel/onSuccess），用于弹框与内容组件通信
    props: Omit<T, keyof ContentComponentProps> & ModalWrapperProps & Partial<ContentComponentProps>
  ) {
    // 解构弹框相关的属性
    const { 
      open,           // 弹框显示状态
      onCancel,       // 弹框关闭回调
      width = "90%",  // 弹框宽度，默认90%
      className = 'report-modal', // 弹框样式类名
      onSuccess,      // 成功回调
      ...restProps    // 其他传递给内容组件的属性
    } = props;

    // 弹框标题状态管理
    const [title, setTitle] = useState<string>('');

    /**
     * 处理标题变更的回调函数
     * 内容组件通过此函数设置弹框标题
     * 
     * @param newTitle - 新的标题文本
     */
    const handleTitleChange = useCallback((newTitle: string) => {
      setTitle(newTitle);
    }, []);

    /**
     * 处理弹框关闭的回调函数
     * 关闭时清空标题并调用外部传入的 onCancel
     */
    const handleCancel = useCallback(() => {
      setTitle(''); // 清空标题状态
      onCancel();   // 调用外部关闭回调
    }, [onCancel]);

    return (
      <Modal
        // 弹框标题，使用 Typography.Title 组件
        title={<Title level={2} style={{ margin: 0 }}>{title}</Title>}
        // 弹框显示状态
        open={open}
        // 弹框关闭处理
        onCancel={handleCancel}
        // 弹框宽度
        width={width}
        // 不显示底部按钮区域
        footer={null}
        // 关闭时销毁子组件
        destroyOnClose
        // 弹框样式类名
        className={className}
      >
        {/* 渲染被包装的内容组件 */}
        <WrappedComponent 
          // 传递所有外部属性给内容组件
          {...(restProps as any)}
          // 传递标题变更回调
          onTitleChange={handleTitleChange}
          // 传递取消回调
          onCancel={handleCancel}
          // 传递成功回调
          onSuccess={onSuccess}
        />
      </Modal>
    );
  };
}

export default withModalWrapper; 