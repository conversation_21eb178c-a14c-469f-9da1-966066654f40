import React from 'react';
import { Card, Typography, List, Tag } from 'antd';

const { Title, Text } = Typography;

interface KeyDateItem {
  start_date: string;
  end_date: string;
  type: string[];
  name: string;
  confidence: string;
  significance: string;
  expected_impact: {
    traffic: string;
    conversion: string;
    competition: string;
    rationale: string;
  };
  strategy: string;
}

interface KeyDatesCardProps {
  keyDates?: KeyDateItem[];
}

const KeyDatesCard: React.FC<KeyDatesCardProps> = ({ keyDates }) => {
  if (!keyDates || keyDates.length === 0) {
    return null;
  }

  return (
    <Card className="card" style={{ marginTop: '24px' }}>
      <Title level={4} style={{ marginBottom: '16px' }}>关键日期</Title>
      <List
        itemLayout="horizontal"
        dataSource={keyDates}
        renderItem={item => (
          <List.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', paddingRight: '20%' }}>
              <Text strong style={{ width: '40%' }}>{item.name}</Text>
              <Text style={{ width: '40%' }}>{`${item.start_date} ~ ${item.end_date}`}</Text>
              <Text style={{ width: '20%' }}>重要性：
                <Tag style={{ fontSize: "16px" }} color={item.significance === 'high' ? 'red' : item.significance === 'medium' ? 'orange' : 'blue'}>
                  {item.significance === 'high' ? '高' : item.significance === 'medium' ? '中' : '低'}
                </Tag>
              </Text>
            </div>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default KeyDatesCard;