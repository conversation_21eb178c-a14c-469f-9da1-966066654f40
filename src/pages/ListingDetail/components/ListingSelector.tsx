import React, { useState, useEffect } from 'react';
import { Modal, List, Button, Select, Input, Space, Image, Rate, Typography } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { getListingsList, getListingFilterOptions } from '@/services/ibidder_api/listings';

const { Text } = Typography;

interface ListingSelectorProps {
  open: boolean;
  onCancel: () => void;
  onSelect: (parentAsin: string, profileId: string) => void;
}

interface FilterOptions {
  countries: string[];
  store_names: string[];
  owners: string[];
  currencies: string[];
  brands: string[];
  statuses: string[];
}

const ListingSelector: React.FC<ListingSelectorProps> = ({ open, onCancel, onSelect }) => {
  const [listings, setListings] = useState<API.ListingInfo[]>([]);
  const [loading, setLoading] = useState(false);

  // 筛选选项状态
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    countries: [],
    store_names: [],
    owners: [],
    currencies: [],
    brands: [],
    statuses: []
  });

  // 筛选条件状态
  const [filters, setFilters] = useState<{
    country?: string;
    store_name?: string;
    owner?: string;
    asin?: string;
  }>({});

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 处理关闭弹框，重置筛选状态
  const handleCancel = () => {
    setFilters({});
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    onCancel();
  };

  // 获取筛选选项
  const fetchFilterOptions = async () => {
    try {
      const response = await getListingFilterOptions();
      const responseData = response.data;

      if (responseData) {
        setFilterOptions({
          countries: responseData.countries || [],
          store_names: responseData.store_names || [],
          owners: responseData.owners || [],
          currencies: responseData.currencies || [],
          brands: responseData.brands || [],
          statuses: responseData.statuses || []
        });
      }
    } catch (error) {
      console.error('获取筛选选项出错:', error);
    }
  };

  // 获取商品列表
  const fetchListings = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: pagination.current,
        page_size: pagination.pageSize
      };

      // 添加筛选参数
      if (filters.country) params.country = filters.country;
      if (filters.store_name) params.store_name = filters.store_name;
      if (filters.owner) params.owner = filters.owner;
      if (filters.asin) params.asin = filters.asin;

      const response = await getListingsList(params);
      const responseData =response.data;

      if (responseData && responseData.listings) {
        setListings(responseData.listings);
        setPagination(prev => ({
          ...prev,
          total: responseData.total || responseData.listings.length
        }));
      }
    } catch (error) {
      console.error('获取商品列表出错:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理筛选条件变化
  const handleFilterChange = (type: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [type]: value
    }));
  };

  // 搜索
  const handleSearch = () => {
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    fetchListings();
  };

  // 处理选择商品
  const handleSelectListing = (parentAsin: string, profileId: string) => {
    onSelect(parentAsin, profileId);
    handleCancel()
  };

  // 监听modal打开状态，获取数据
  useEffect(() => {
    if (open) {
      fetchFilterOptions();
      fetchListings();
    }
  }, [open]);

  // 监听分页变化
  useEffect(() => {
    if (open && pagination.current > 1) {
      fetchListings();
    }
  }, [pagination.current, pagination.pageSize]);

  // 筛选区域组件
  const FilterArea = () => (
    <Space wrap size="middle" style={{ margin: '10px 0' }}>
      <Select
        value={filters.country}
        onChange={(value) => handleFilterChange('country', value)}
        style={{ width: 120 }}
        placeholder="全部国家"
        allowClear
      >
        {filterOptions.countries.map(country => (
          <Select.Option key={country} value={country}>{country}</Select.Option>
        ))}
      </Select>

      <Select
        value={filters.store_name}
        onChange={(value) => handleFilterChange('store_name', value)}
        style={{ width: 120 }}
        placeholder="全部店铺"
        allowClear
      >
        {filterOptions.store_names.map(store => (
          <Select.Option key={store} value={store}>{store}</Select.Option>
        ))}
      </Select>

      <Select
        value={filters.owner}
        onChange={(value) => handleFilterChange('owner', value)}
        style={{ width: 120 }}
        placeholder="负责人"
        allowClear
      >
        {filterOptions.owners.map(owner => (
          <Select.Option key={owner} value={owner}>{owner}</Select.Option>
        ))}
      </Select>

      <Input
        value={filters.asin}
        onChange={(e) => handleFilterChange('asin', e.target.value)}
        style={{ width: 120 }}
        placeholder="搜索ASIN"
        allowClear
      />

      <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
        搜索
      </Button>
    </Space>
  );

  return (
    <Modal
      title="选择商品"
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={900}
    >
      <FilterArea />
      <List
        loading={loading}
        itemLayout="horizontal"
        dataSource={listings}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: false,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({
              ...prev,
              current: page,
              pageSize: pageSize || prev.pageSize
            }));
          }
        }}
        renderItem={(item) => (
          <List.Item
            key={item.asin}
            actions={[
              <Button
                key="select"
                type="primary"
                onClick={() => handleSelectListing(item.parent_asin, item.profile_id+'')}
              >
                选择
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={
                <Image
                  width={80}
                  height={80}
                  src={item.image_url}
                  alt={item.title}
                  preview={true}
                />
              }
              title={item.title}
              description={
                <Space direction='horizontal' size="middle">
                  <Text>ASIN: {item.asin}</Text>
                  <Text>价格: ${item.price}</Text>
                  <Rate allowHalf value={item.rating} disabled />
                  <Text>{item.rating} ({item.review_count}条评价)</Text>
                </Space>
              }
            />
          </List.Item>
        )}
      />
    </Modal>
  );
};

export default ListingSelector; 