import React from 'react';
import { Card, Row, Col } from 'antd';
import './style.less';

interface WeekStrategyInfoProps {
  weekApproach: string;
  weekRationale: string;
  weekBudget: {
    typical: number;
    min: number;
    max: number;
    rationale: string;
  };
  keywordBidStrategy: {
    adjustment_range: {
      min: string;
      max: string;
    };
    rational: string;
  };
  adSpend: number;
  adSales: number;
  acos: number;
  conversionRate: number;
  adSpendRange: string;
  adSalesRange: string;
  acosRange: string;
  conversionRateRange: string;
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
  threats: string[];
}

const WeekStrategyInfo: React.FC<WeekStrategyInfoProps> = ({
  weekApproach,
  weekBudget,
  keywordBidStrategy,
  adSpend,
  adSales,
  acos,
  conversionRate,
  adSpendRange,
  adSalesRange,
  acosRange,
  conversionRateRange,
  strengths,
  weaknesses,
  opportunities,
  threats,
}) => {
  return (
    <Card className="weekStrategyInfo">
      <div className="strategyHeader">
        <div className="strategyItem">
          <div className="title">总体策略</div>
          <div className="content">{weekApproach}</div>
        </div>
        <div className="strategyItem">
          <div className="title">周预算</div>
          <div className="content">${weekBudget.typical}</div>
          <div className="range">${weekBudget.min} - ${weekBudget.max}</div>
        </div>
        <div className="strategyItem">
          <div className="title">竞价调整</div>
          <div className="content">{keywordBidStrategy.adjustment_range.min} - {keywordBidStrategy.adjustment_range.max}</div>
        </div>
      </div>

      <div className="resultSection">
        <Row gutter={24}>
          <Col span={6}>
            <div className="resultItem">
              <div className="value">${adSpend}</div>
              <div className="label">广告支出</div>
              <div className="range">{adSpendRange}</div>
            </div>
          </Col>
          <Col span={6}>
            <div className="resultItem">
              <div className="value">${adSales}</div>
              <div className="label">广告销售额</div>
              <div className="range">{adSalesRange}</div>
            </div>
          </Col>
          <Col span={6}>
            <div className="resultItem">
              <div className="value">{acos}%</div>
              <div className="label">ACoS</div>
              <div className="range">{acosRange}</div>
            </div>
          </Col>
          <Col span={6}>
            <div className="resultItem">
              <div className="value">{conversionRate}%</div>
              <div className="label">转化率</div>
              <div className="range">{conversionRateRange}</div>
            </div>
          </Col>
        </Row>
      </div>

      <div className="marketAnalysis">
        <div className="analysisCard strengths">
          <div className="title">优势 (Strengths)</div>
          <ul>
            {strengths.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
        <div className="analysisCard weaknesses">
          <div className="title">劣势 (Weaknesses)</div>
          <ul>
            {weaknesses.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
        <div className="analysisCard opportunities">
          <div className="title">机会 (Opportunities)</div>
          <ul>
            {opportunities.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
        <div className="analysisCard threats">
          <div className="title">威胁 (Threats)</div>
          <ul>
            {threats.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
      </div>
    </Card>
  );
};

export default WeekStrategyInfo;