// 趋势类型
export type TrendType = 'up' | 'down';

// 数据类型
export type ValueType = 'money' | 'percentage' | 'number';

// 趋势数据接口
export interface TrendData {
  value: number;
  type: TrendType;
  label: string;
}

// 统计卡片属性接口
export interface StatisticCardProps {
  title: string;
  mainValue: number;
  valueType: ValueType;
  icon: React.ReactNode;
  weeklyTrend?: TrendData;
  dailyTrend?: TrendData;
  footerData?: {
    label: string;
    value: number;
    suffix?: string;
  };
  chartType?: 'area' | 'column' | 'progress';
  chartData?: Array<{ date: string; value: number }>;
  chartConfig?: {
    height: number;
    padding: number[];
  };
}

// 图表数据类型
export interface ChartDataType {
  date: string;
  value: number;
}

// 图表配置接口
export interface ChartConfig {
  height?: number;
  padding?: number[];
  autoFit?: boolean;
}

// SWOT分析数据接口
export interface SwotData {
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
  threats: string[];
}

// 预期结果范围接口
export interface MetricRange {
  typical: number;
  min: number;
  max: number;
}

// 预期结果接口
export interface ExpectedResults {
  spend: MetricRange;
  sales: MetricRange;
  orders: MetricRange;
  cvr: MetricRange;
  acos: MetricRange;
}

// 周策略数据接口
export interface WeekStrategyData {
  target_week_strategy: {
    week_swot: SwotData;
    week_expected_result: ExpectedResults;
    week_start_date: string;
    week_end_date: string;
    week_approach: string;
    week_budget: {
      typical: number;
      min: number;
      max: number;
      rationale: string;
    };
    week_ads_goal: {
      primary_goal: {
        goal: string;
        rationale: string;
      };
      other_goals: string[];
    };
    week_forecast: {
      market_preview: string[];
      metrics_forecast: Record<string, string>;
    };
  };
  other_suggestions_for_seller: string[];
}