import React from 'react';
import { Table, Tag, Tooltip } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { WeekStrategyData } from '../../types';

interface CampaignStrategyProps {
  campaigns: WeekStrategyData['target_week_strategy']['campaigns'];
}

const CampaignStrategy: React.FC<CampaignStrategyProps> = ({ campaigns }) => {
  const columns = [
    {
      title: '广告活动',
      dataIndex: 'campaign_name',
      key: 'campaign_name',
      width: 200,
      fixed: 'left' as const,
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (state: string) => (
        <Tag color={state === 'enabled' ? 'green' : 'red'}>
          {state === 'enabled' ? '启用' : '暂停'}
        </Tag>
      ),
    },
    {
      title: (
        <span>
          预算占比
          <Tooltip title="当前预算在总预算中的占比">
            <InfoCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        </span>
      ),
      dataIndex: 'daily_budget_ratio',
      key: 'daily_budget_ratio',
      width: 100,
      render: (ratio: string) => `${(parseFloat(ratio) * 100).toFixed(1)}%`,
    },
    {
      title: '建议',
      dataIndex: 'campaign_suggestion',
      key: 'campaign_suggestion',
      width: 200,
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      key: 'rationale',
      ellipsis: true,
    },
  ];

  return (
    <ProCard
      title="广告活动策略"
      headerBordered
      bordered
    >
      <Table
        columns={columns}
        dataSource={campaigns}
        rowKey="campaign_id"
        scroll={{ x: 1000 }}
        pagination={false}
        size="small"
      />
    </ProCard>
  );
};

export default CampaignStrategy;