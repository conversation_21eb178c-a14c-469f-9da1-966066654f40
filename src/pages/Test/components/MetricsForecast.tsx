import React, { useMemo } from 'react';
import { ProCard } from '@ant-design/pro-components';
import { Row, Col, Statistic } from 'antd';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';

interface MetricsForecastProps {
  metrics: Record<string, string>;
}

const MetricsForecast: React.FC<MetricsForecastProps> = ({ metrics }) => {
  const formattedMetrics = useMemo(() => {
    return Object.entries(metrics).map(([key, value]) => ({
      key,
      value,
      trend: value.toLowerCase(),
      color: value.toLowerCase() === 'up' ? '#52c41a' : value.toLowerCase() === 'down' ? '#ff4d4f' : '#1890ff',
      icon: value.toLowerCase() === 'up' ? <CaretUpOutlined /> : value.toLowerCase() === 'down' ? <CaretDownOutlined /> : null
    }));
  }, [metrics]);

  if (!metrics || Object.keys(metrics).length === 0) {
    return null;
  }

  return (
    <ProCard title="指标预测" className="metrics-card" headerBordered>
      <Row gutter={[16, 16]}>
        {formattedMetrics.map(({ key, trend, color, icon }) => (
          <Col span={8} key={key}>
            <Statistic
              title={key.toUpperCase()}
              value={trend.charAt(0).toUpperCase() + trend.slice(1)}
              valueStyle={{ color }}
              prefix={icon}
            />
          </Col>
        ))}
      </Row>
    </ProCard>
  );
};

export default MetricsForecast;