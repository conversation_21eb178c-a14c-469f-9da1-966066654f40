import React from 'react';
import { Card, Typography, Row, Col, Statistic } from 'antd';
import { LineChartOutlined, CaretUpOutlined, CaretDownOutlined, MinusOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface MetricsForecastProps {
  metrics: Record<string, 'up' | 'down' | 'stable'>;
}

const MetricsForecast: React.FC<MetricsForecastProps> = ({ metrics }) => {
  const getMetricIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <CaretUpOutlined style={{ color: '#52c41a' }} />;
      case 'down':
        return <CaretDownOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <MinusOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getMetricColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return '#52c41a';
      case 'down':
        return '#ff4d4f';
      default:
        return '#1890ff';
    }
  };

  return (
    <Card
      className="metrics-forecast-card"
      style={{
        height: '100%',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
        transition: 'all 0.3s ease',
      }}
      hoverable
    >
      <div style={{ padding: '8px 0' }}>
        <Title 
          level={4} 
          style={{ 
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <LineChartOutlined style={{ color: '#1890ff' }} />
          指标预测
        </Title>
        <Row gutter={[16, 16]}>
          {Object.entries(metrics).map(([metric, trend]) => (
            <Col span={8} key={metric}>
              <Card
                style={{
                  textAlign: 'center',
                  background: '#fafafa',
                  border: 'none',
                  borderRadius: '8px',
                  transition: 'all 0.3s ease',
                }}
                bodyStyle={{ padding: '12px' }}
              >
                <Statistic
                  title={<span style={{ fontSize: '14px' }}>{metric.toUpperCase()}</span>}
                  value={trend.toUpperCase()}
                  valueStyle={{
                    color: getMetricColor(trend),
                    fontSize: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '4px'
                  }}
                  prefix={getMetricIcon(trend)}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </Card>
  );
};

export default MetricsForecast;