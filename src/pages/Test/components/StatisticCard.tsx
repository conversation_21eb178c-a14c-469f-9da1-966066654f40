import React from 'react';
import { Card, Space, Typography, Statistic } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { VisitChart, PaymentChart, ActivityProgress } from './Charts';
import type { StatisticCardProps } from '../types';

const { Text } = Typography;

export const StatisticCard: React.FC<StatisticCardProps> = ({
  title,
  mainValue,
  valueType,
  icon,
  weeklyTrend,
  dailyTrend,
  footerData,
  chartType = 'area',
  chartData,
  chartConfig,
}) => {
  const formatValue = (value: number) => {
    switch (valueType) {
      case 'money':
        return `$${value.toFixed(2)}`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toFixed(0);
    }
  };

  const renderTrend = (trend?: { value: number; type: 'up' | 'down'; label: string }) => {
    if (!trend) return null;

    const { value, type, label } = trend;
    const color = type === 'up' ? '#52c41a' : '#ff4d4f';
    const Icon = type === 'up' ? ArrowUpOutlined : ArrowDownOutlined;

    return (
      <Space>
        <Text type="secondary">{label}:</Text>
        <Text style={{ color }}>
          <Icon /> {value}%
        </Text>
      </Space>
    );
  };

  const renderChart = () => {
    if (!chartData) return null;

    switch (chartType) {
      case 'area':
        return <VisitChart data={chartData} config={chartConfig} />;
      case 'column':
        return <PaymentChart data={chartData} config={chartConfig} />;
      case 'progress':
        return <ActivityProgress percent={mainValue} />;
      default:
        return null;
    }
  };

  return (
    <Card className="statistic-card" bodyStyle={{ padding: '20px' }}>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <Space align="center">
          <span className="card-icon">{icon}</span>
          <Text type="secondary">{title}</Text>
        </Space>

        <Statistic
          value={mainValue}
          formatter={(value) => formatValue(value as number)}
          valueStyle={{ fontSize: 24 }}
        />

        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {renderTrend(weeklyTrend)}
          {renderTrend(dailyTrend)}
        </Space>

        <div className="chart-container" style={{ height: 60 }}>
          {renderChart()}
        </div>

        {footerData && (
          <div className="footer-data">
            <Text type="secondary">
              {footerData.label}: {footerData.value}
              {footerData.suffix && ` ${footerData.suffix}`}
            </Text>
          </div>
        )}
      </Space>
    </Card>
  );
};