import React from 'react';
import { ProCard } from '@ant-design/pro-components';
import { List, Typography } from 'antd';

interface MarketPreviewProps {
  previewData: string[];
}

const MarketPreview: React.FC<MarketPreviewProps> = ({ previewData }) => {
  return (
    <ProCard title="市场预览" className="preview-card" headerBordered>
      <List
        dataSource={previewData}
        renderItem={(item, index) => (
          <List.Item key={index}>
            <Typography.Text>{item}</Typography.Text>
          </List.Item>
        )}
      />
    </ProCard>
  );
};

export default MarketPreview;