import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { getListing, saveListing, getListingFilterOptions } from '@/services/ibidder_api/listings';
import ListingForm from '@/pages/Admin/ListingManagement/components/ListingForm';
import SettingForm from '@/pages/Admin/ListingManagement/components/SettingForm';
import { getResizedAmazonImageUrl } from '@/utils/common';

interface AddListingModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
}

const defaultInitialValues = {
  day_target_sales: null,
  target_acos: null,
  daily_budget_suggest: null,
  approach_bias: 'balanced',
  daily_budget_range: { min: 10, max: null },
  campaign_budget_range: { min: 5, max: 100 },
  campaign_bid_range: { min: -10, max: 10 },
  placement_bid_range: { min: 0, max: 30 },
  hod_bid_range: { min: -20, max: 10 },
};

const AddListingModal: React.FC<AddListingModalProps> = ({ open, onCancel: parentOnCancel, onOk }) => {
  const [form] = Form.useForm();
  const [confirmForm] = Form.useForm();
  const [settingForm] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [stepData, setStepData] = useState<any>({});
  const [aiData, setAiData] = useState<any>({});
  const [product, setProduct] = useState<{
    asin: string;
    title: string;
    price: number;
    rating: number;
    reviewCount: number;
    imageUrl: string;
    country: string;
    url: string;
    storeName: string;
  }>({
    asin: '',
    title: '',
    price: 0,
    rating: 0,
    reviewCount: 0,
    imageUrl: '',
    country: 'US',
    url: '',
    storeName: ''
  });
  const [stepOpen, setStepOpen] = useState(false);
  const [hasPromotions, setHasPromotions] = useState(false);

  // 添加筛选选项状态
  const [filterOptions, setFilterOptions] = useState<{
    store_names: string[];
    seller_list: {
      profile_id: string;
      country: string;
      store_name: string;
    }[];
  }>({
    store_names: [],
    seller_list: []
  });

  const [settingOpen, setSettingOpen] = useState(false);

  // 获取筛选选项
  const fetchFilterOptions = async () => {
    try {
      const response = await getListingFilterOptions();
      const responseData = response.data;

      if (responseData) {
        setFilterOptions({
          store_names: responseData.store_names || [],
          seller_list: responseData.seller_list || []
        });
      }
    } catch (error) {
      console.error('获取筛选选项出错:', error);
    }
  };

  // 在弹窗打开时获取数据和筛选选项
  useEffect(() => {
    if (open) {
      fetchFilterOptions();
    }
  }, [open]);

  const resetModalState = () => {
    form.resetFields();
    confirmForm.resetFields();
    confirmForm.setFieldsValue({
      inventory_status: 'normal'
    });
    setStepOpen(false);
    setHasPromotions(false);
  };

  const resetStepData = () => {
    confirmForm.resetFields();
    confirmForm.setFieldsValue({
      inventory_status: 'normal'
    });
    setHasPromotions(false);
  }

  const handleOk = () => {
    form
      .validateFields()
      .then(() => {
        setLoading(true);
        const params = form.getFieldsValue();
        getListing(params).then((res: any) => {
          if (res.code !== 200) {
            message.error(res.message);
            return;
          }
          const productData = res.data[0];
          setStepData(productData);
          setProduct({
            asin: productData.parent_asin || '',
            title: productData.title || '',
            price: productData.price || 0,
            rating: productData.rating || 0,
            reviewCount: productData.review_count || 0,
            imageUrl: getResizedAmazonImageUrl(productData.image_url || ''),
            country: productData.country || 'US',
            url: productData.url || '',
            storeName: productData.store_name || ''
          });
          setStepOpen(true);
          resetStepData();
        })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch((info) => {
        console.log('Validate Failed:', info.message);
      });
  };


  const handleCancel = () => {
    settingForm.setFieldsValue(defaultInitialValues);
    setSettingOpen(false);
    resetModalState();
    parentOnCancel();
  };

  const goToSetting = () => {
    confirmForm.validateFields().then(() => {
      const confirmValues = confirmForm.getFieldsValue();
      setAiData(confirmValues);
      setStepOpen(false);
      setSettingOpen(true);
    });
  };

  const goToListing = () => {
    setSettingOpen(false);
    setStepOpen(true);
  };

  const handleConfirm = () => {
    settingForm.validateFields().then(() => {
      const settingValues = settingForm.getFieldsValue();
      const finalData = {
        ...stepData,
        ...aiData,
        ads_rules: settingValues
      };
      setLoading(true);
      saveListing(finalData)
        .then((res: any) => {
          if (res.code === 200) {
            message.success('添加Listing成功');
            handleCancel();
            onOk();
          } else {
            message.error(res.message || '添加Listing失败');
          }
        })
        .catch((error) => {
          console.error('添加Listing出错:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <>
      <Modal
        title="添加Listing"
        open={open && !stepOpen}
        onCancel={handleCancel}
        onOk={handleOk}
        width={480}
        okText="下一步"
        cancelText="取消"
        bodyStyle={{ paddingTop: 16 }}
        destroyOnClose
        confirmLoading={loading}
        centered
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="选择店铺"
            name="profile_id"
            rules={[{ required: true, message: '请选择店铺' }]}
          >
            <Select placeholder="请选择">
              {filterOptions.seller_list.map(seller => (
                <Select.Option key={seller.profile_id} value={seller.profile_id}>
                  {seller.store_name} - {seller.country}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="ASIN"
            name="asin"
            rules={[{ required: true, message: '请输入ASIN' }]}
          >
            <Input placeholder="在此输入您需要添加的ASIN" />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        open={stepOpen}
        onCancel={() => setStepOpen(false)}
        onOk={goToSetting}
        width={1280}
        okText="下一步"
        cancelText="返回上一步"
        bodyStyle={{ paddingTop: 16 }}
        destroyOnClose
        confirmLoading={loading}
        centered
      >
        <ListingForm
          form={confirmForm}
          product={product}
          hasPromotions={hasPromotions}
          onPromotionChange={setHasPromotions}
          showProductInfo={true}
        />
      </Modal>
      <Modal
        open={settingOpen}
        onCancel={goToListing}
        onOk={handleConfirm}
        width={1280}
        okText="保存"
        cancelText="返回上一步"
        bodyStyle={{ paddingTop: 16 }}
        destroyOnClose
        confirmLoading={loading}
        centered
      >
        <SettingForm
          form={settingForm}
          showHeader={true}
          initialValues={defaultInitialValues}
        />
      </Modal>
    </>
  );
};

export default AddListingModal;
