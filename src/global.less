html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

.color-primary {
  color: #ed1000;

  &:hover {
    color: #ed1000bd;
  }
}

.btn-primary {
  background-color: #d800271a;
  border-color: #d800271a;
  color: #ED1000;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead>tr,
    &-tbody>tr {

      >th,
      >td {
        white-space: pre;

        >span {
          display: block;
        }
      }
    }
  }
}

.card {
  flex-direction: column;
  border-radius: 14px;
  box-shadow: 0px 3px 10px 0px rgba(31, 31, 31, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  background-color: #fff;

  &.noHover {
    &:hover {
      box-shadow: 0px 3px 10px 0px rgba(31, 31, 31, 0.08);
      transform: translateY(0);
    }
  }

  &:hover {
    box-shadow: 0 2px 8px 1px rgba(64, 60, 67, .24);
    transform: translateY(-2px);
  }
}

.goalList {
  list-style: none;
  padding: 0;
  margin: 0;

  .goalDot {
    width: 6px;
    height: 6px;
    background-color: #000000;
    border-radius: 50%;
    margin-right: 8px;
    margin-top: 6px;
    flex-shrink: 0;
  }

  .goalItem {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.report-modal {
  .ant-modal-content {
    padding: 20px 0px 20px 0px;
  }

  .ant-modal-title {
    padding-left: 20px;
  }

  .report-modal-div {
    .report-modal-content {
      padding: 0 20px;
    }

    .confirmBtn {
      display: flex;
      flex-direction: column;
      margin-top: 16px;
      position: sticky;
      bottom: 0px;
      z-index: 10;
      padding: 16px 20px;
      background-color: #fafafa;
      border-radius: 0 0 8px 8px;
      gap: 16px;
      box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: -20px;
    }
  }
}