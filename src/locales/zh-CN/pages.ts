export default {
  'pages.layouts.userLayout.title': ' ',
  'pages.login.accountLogin.tab': '账户密码登录',
  'pages.login.accountLogin.errorMessage': '账户或密码错误',
  'pages.login.failure': '登录失败，请重试！',
  'pages.login.success': '登录成功！',
  'pages.login.username.placeholder': '用户名',
  'pages.login.username.required': '用户名是必填项！',
  'pages.login.password.placeholder': '密码',
  'pages.login.password.required': '密码是必填项！',
  'pages.login.phoneLogin.tab': '手机号登录',
  'pages.login.phoneLogin.errorMessage': '验证码错误',
  'pages.login.phoneNumber.placeholder': '请输入手机号！',
  'pages.login.phoneNumber.required': '手机号是必填项！',
  'pages.login.phoneNumber.invalid': '不合法的手机号！',
  'pages.login.captcha.placeholder': '请输入验证码！',
  'pages.login.captcha.required': '验证码是必填项！',
  'pages.login.phoneLogin.getVerificationCode': '获取验证码',
  'pages.getCaptchaSecondText': '秒后重新获取',
  'pages.login.rememberMe': '自动登录',
  'pages.login.forgotPassword': '忘记密码 ?',
  'pages.login.submit': '登录',
  'pages.login.loginWith': '其他登录方式 :',
  'pages.login.registerAccount': '注册账户',
  'pages.welcome.link': '欢迎使用',
  'pages.welcome.alertMessage': '更快更强的重型组件，已经发布。',
  'pages.404.subTitle': '抱歉，您访问的页面不存在。',
  'pages.404.buttonText': '返回首页',
  'pages.admin.subPage.title': ' 这个页面只有 admin 权限才能查看',
  'pages.admin.subPage.alertMessage': 'umi ui 现已发布，欢迎使用 npm run ui 启动体验。',
  'pages.searchTable.createForm.newRule': '新建规则',
  'pages.searchTable.updateForm.ruleConfig': '规则配置',
  'pages.searchTable.updateForm.basicConfig': '基本信息',
  'pages.searchTable.updateForm.ruleName.nameLabel': '规则名称',
  'pages.searchTable.updateForm.ruleName.nameRules': '请输入规则名称！',
  'pages.searchTable.updateForm.ruleDesc.descLabel': '规则描述',
  'pages.searchTable.updateForm.ruleDesc.descPlaceholder': '请输入至少五个字符',
  'pages.searchTable.updateForm.ruleDesc.descRules': '请输入至少五个字符的规则描述！',
  'pages.searchTable.updateForm.ruleProps.title': '配置规则属性',
  'pages.searchTable.updateForm.object': '监控对象',
  'pages.searchTable.updateForm.ruleProps.templateLabel': '规则模板',
  'pages.searchTable.updateForm.ruleProps.typeLabel': '规则类型',
  'pages.searchTable.updateForm.schedulingPeriod.title': '设定调度周期',
  'pages.searchTable.updateForm.schedulingPeriod.timeLabel': '开始时间',
  'pages.searchTable.updateForm.schedulingPeriod.timeRules': '请选择开始时间！',
  'pages.searchTable.titleDesc': '描述',
  'pages.searchTable.ruleName': '规则名称为必填项',
  'pages.searchTable.titleCallNo': '服务调用次数',
  'pages.searchTable.titleStatus': '状态',
  'pages.searchTable.nameStatus.default': '关闭',
  'pages.searchTable.nameStatus.running': '运行中',
  'pages.searchTable.nameStatus.online': '已上线',
  'pages.searchTable.nameStatus.abnormal': '异常',
  'pages.searchTable.titleUpdatedAt': '上次调度时间',
  'pages.searchTable.exception': '请输入异常原因！',
  'pages.searchTable.titleOption': '操作',
  'pages.searchTable.config': '配置',
  'pages.searchTable.subscribeAlert': '订阅警报',
  'pages.searchTable.title': '查询表格',
  'pages.searchTable.new': '新建',
  'pages.searchTable.chosen': '已选择',
  'pages.searchTable.item': '项',
  'pages.searchTable.totalServiceCalls': '服务调用次数总计',
  'pages.searchTable.tenThousand': '万',
  'pages.searchTable.batchDeletion': '批量删除',
  'pages.searchTable.batchApproval': '批量审批',
  
  // 忘记密码页面
  'pages.forgotPassword.title': '忘记密码',
  'pages.forgotPassword.resetTitle': '重置密码',
  'pages.forgotPassword.smsVerification': '短信验证（真实手机号注册）',
  'pages.forgotPassword.emailVerification': '邮箱验证',
  'pages.forgotPassword.backToLogin': '返回登录页面',
  'pages.forgotPassword.phone.required': '请输入手机号！',
  'pages.forgotPassword.phone.placeholder': '请输入手机号',
  'pages.forgotPassword.phone.invalid': '请输入有效的手机号！',
  'pages.forgotPassword.email.required': '请输入邮箱地址！',
  'pages.forgotPassword.email.placeholder': '请输入邮箱地址',
  'pages.forgotPassword.email.invalid': '请输入有效的邮箱地址！',
  'pages.forgotPassword.captcha.required': '请输入验证码！',
  'pages.forgotPassword.captcha.placeholder': '请输入验证码',
  'pages.forgotPassword.getCaptchaBtn': '获取验证码',
  'pages.forgotPassword.getCaptcha.success': '验证码发送成功！',
  'pages.forgotPassword.getCaptcha.failed': '验证码发送失败，请重试！',
  'pages.forgotPassword.resetPasswordBtn': '重置密码',
  'pages.forgotPassword.newPassword.required': '请输入新密码！',
  'pages.forgotPassword.newPasswordPlaceholder': '新密码',
  'pages.forgotPassword.newPassword.minLength': '密码至少6位！',
  'pages.forgotPassword.confirmPassword.required': '请确认新密码！',
  'pages.forgotPassword.confirmPasswordPlaceholder': '新密码二次确认',
  'pages.forgotPassword.password.mismatch': '两次输入的密码不一致！',
  'pages.forgotPassword.confirmBtn': '确认',
  'pages.forgotPassword.reset.success': '密码重置成功！请使用新密码登录。',
  'pages.forgotPassword.reset.failed': '密码重置失败，请重试！',
};
