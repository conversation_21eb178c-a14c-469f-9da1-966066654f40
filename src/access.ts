/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(initialState: { currentUser?: any } | undefined) {
  const { currentUser } = initialState ?? {};
  
  // 直接使用布尔值判断
  if (process.env.REACT_APP_NO_AUTH) {
    return {
      canAdmin: true,
      normalUser: true,
    };
  }

  return {
    canAdmin: currentUser && currentUser.access === 'admin',
    normalUser: currentUser && currentUser.access === 'user',
  };
}
