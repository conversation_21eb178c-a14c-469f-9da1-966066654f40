// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：

import { request } from '@/request';
import { request as umiRequest } from '@umijs/max';

/**
 * 账号登录
 */
export async function accountLogin(params: API.AccountLoginParams) {
  return request('/api/v1/auth/login', {
    method: 'POST',
    data: params,
  });
}

// 发送动态验证码
export async function getDynamicCode(params: API.GetDynamicCodeParams) {
  return request('/api/v1/auth/dynamic-code', {
    method: 'POST',
    data: params,
  });
}

// 注册
export async function register(params: API.RegisterParams) {
  return request('/api/v1/auth/register', {
    method: 'POST',
    data: params,
  });
}

export interface GetRegistrationNotice {
    companyName: string;  // 公司名称
    companyType: string;  // 公司类型
    monthlyAdSpend: string;  // 预估月度广告支出
    name: string;  // 姓名
    phoneNumber: string;  // 电话号码
    workEmail: string;  // 工作邮箱
    [property: string]: any;
}
// 注册成功后获取注册通知 POST /api/v1/registration/notice
export async function getRegistrationNotice(params:GetRegistrationNotice) {
  return request('/api/v1/registration/notice', {
    method: 'POST',
    data: params,
  });
}

/** 获取消息列表 POST /api/v1/messages/list */
export async function getMessages(
  body: {
    /*0-未读 1-已读 */
    is_read?: number;
    /* 消息类型：1系统通知 2私信 3评论 4点赞 5任务提醒*/
    message_type?: number;
    page_no: number;
    page_size: number;
    parent_asin: string,
    profile_id: string,
  },
  options?: { [key: string]: any },
) {
  return request<API.MessageListResponse>('/api/v1/messages/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户设置信息 GET /api/v1/settings/info */
export async function getSettings() {
  return request<any>('/api/v1/settings/info', {
    method: 'GET',
  });
}

/** 更新用户设置 POST /api/v1/settings/update */
export async function updateSettings(params: any) {
  return request<any>('/api/v1/settings/update', {
    method: 'POST',
    data: params,
  });
}