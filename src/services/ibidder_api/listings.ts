// @ts-ignore
/* eslint-disable */
import { request } from '@/request';

/** Get Listing Info GET /api/v1/listings/info */
export async function getListingInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetListingInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ListingInfo>('/api/v1/listings/info', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Get Listings List 获取所有listings的基本信息列表 POST /api/v1/listings/list */
export async function getListingsList(options?:{
  page?: number;
  page_size?: number;
  parent_asin?: string;
  asin?: string;
  title?: string;
  status?: string;
  country?: string;
  store_name?: string;
  owner?: string;
  currency?: string;
  brand?: string;
}) {
  return request<{
    listings: API.ListingInfo[];
    total: number;
    total_pages: number;
  }>('/api/v1/listings/list', {
    method: 'POST',
    data: {
      ...options,
    },
  });
}

/** 查询报告是否生成 POST /api/v1/listings/report_create_status */
export async function getReportCreateStatus(params: {
  data: {
    parent_asin: string;
    profile_id: number;
    country: string;
  }[];
}) {
  return request<{
    data: {
      parent_asin: string;
      profile_id: number;
      country: string;
      report_status: boolean;
    }[];
  }>('/api/v1/listings/report_create_status', {
    method: 'POST',
    data: params,
  });
}

/** 获取亚马逊临时产品数据 GET /api/v1/listings/temp-amazon-data */
export async function getTempAmazonData(params?: {
  page?: number;
  page_size?: number;
  parent_asin?: string;
  title?: string;
  status?: string;
  country?: string;
  store_name?: string;
  owner?: string;
  currency?: string;
  brand?: string;
}) {
  return request<{
    listings: {
      parent_asin: string;
      title: string;
      price: number;
      currency: string;
      rating: number;
      review_count: number;
      image_url: string;
      url: string;
      status: string;
      store_name: string;
      country: string;
      available_inventory: number;
      acoas: number;
      store_orders: number;
      ad_orders: number;
      owner: string;
      profile_id: number;
      brand: string;
    }[];
    total: number;
    total_pages: number;
  }>('/api/v1/listings/temp-amazon-data', {
    method: 'GET',
    params,
  });
}

/** 批量创建Listing POST /api/v1/listings/batch-create */
export async function batchCreateListing(data: { listings: any[] }) {
  return request<API.OperationResult>('/api/v1/listings/batch-create', {
    method: 'POST',
    data,
  });
}

/** 批量删除Listing POST /api/v1/listings/batch_delete */
export async function batchDeleteListing(data: {
  listings: {
    parent_asin: string;
    profile_id: number;
  }[];
}) {
  return request<{
    success_count: number;
  }>('/api/v1/listings/batch_delete', {
    method: 'POST',
    data,
  });
}

/** 获取筛选选项 GET /api/v1/listings/filter_options */
export async function getListingFilterOptions() {
  return request<API.FilterOptions>('/api/v1/listings/filter_options', {
    method: 'GET',
  });
}

/** 更新Listing信息 PUT /api/v1/listings/update */
export async function updateListing(data: {
  parent_asin: string;
  profile_id: number;
  owners?: string[];
  [key: string]: any;
}) {
  return request<any>('/api/v1/listings/update', {
    method: 'PUT',
    data,
  });
}

/** 获取ASIN信息 POST /api/v1/listings/get_listing */
export async function getListing(data: {
  asin: string;
  profile_id: string;
}) {
  return request<API.ListingData>('/api/v1/listings/get_listing', {
    method: 'POST',
    data,
  });
}

/** 添加listing POST /api/v1/listings/save_listing */
export async function saveListing(data: {
  ad_target: string;
  promotion_plan: {
    has_promotion: boolean;
    time_range?: string[];
    promotion_type?: string;
    discount_price?: number;
  };
  inventory_status: {
    sufficient_inventory: boolean;
    quantity?: number;
  };
  competitor: string[];
  ads_rules: any;
}) {
  return request<API.SavelistingResult>('/api/v1/listings/save_listing', {
    method: 'POST',
    data,
  });
}

/** 获取listing的设置信息 GET /api/v1/listings/setting */
export async function getListingSetting(params: {
  parent_asin: string;
  profile_id: number;
}) {
  return request<any>('/api/v1/listings/setting', {
    method: 'GET',
    params,
  });
}

/** 更新listing的设置信息 POST /api/v1/listings/update_setting */
export async function updateListingSetting(data: {
  parent_asin: string;
  profile_id: number;
  ads_operation?: boolean;
  ads_rules?: any;
}) {
  return request<any>('/api/v1/listings/update_setting', {
    method: 'POST',
    data,
  });
}