import React from 'react';

interface IFooterProps {
  style?: React.CSSProperties;
}
const Footer: React.FC<IFooterProps> = (props) => {
  return <div style={{
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
    gap: '0.4em',
    color: '#8c8c8c',
    fontSize: '14px',
    ...props.style
  }}>
    <span>Copyright © 2025 珠海横琴飞擎动力科技有限公司 FlyPower.AI </span>
    <span>|</span>
    <span>联系我们: <a href="mailto:<EMAIL>" style={{ color: 'inherit' }}><EMAIL></a></span>
  </div>
};

export default Footer;
