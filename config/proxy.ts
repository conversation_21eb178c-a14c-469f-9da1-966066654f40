/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  dev: {
    '/api/v1': {
      // target: 'http://127.0.0.1:3000',
      // 测试环境
      target: 'http://***************:8000',
      // 王聪瑞电脑 ip
      // target: 'http://*************:8000',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: false,
      ws: false,
      onError: (err: any, _req: any, _res: any) => {
        console.error('Proxy error:', err);
      },
    },
    '/api/': {
      target: 'http://***************:8000',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: false,
      ws: false,
      onError: (err: any, _req: any, _res: any) => {
        console.error('Proxy error:', err);
      },
    },
  },
  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: false,
      ws: false,
      onError: (err: any, _req: any, _res: any) => {
        console.error('Proxy error:', err);
      },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: false,
      ws: false,
      onError: (err: any, req: any, _res: any) => {
        console.error('Proxy error:', err);
      },
    },
  },
};
